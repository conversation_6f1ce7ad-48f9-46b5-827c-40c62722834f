package com.example.castapp.utils

import android.view.View
import com.example.castapp.utils.AppLog

/**
 * 🎯 坐标映射管理器 - 根本性解决坐标映射精度问题
 * 
 * 统一管理遥控端和接收端之间的坐标转换，确保精确的坐标映射
 * 
 * 核心原则：
 * 1. 统一的坐标系定义
 * 2. 精确的缩放比例计算
 * 3. 实时的坐标转换验证
 */
class CoordinateMappingManager {
    
    // 接收端屏幕信息
    private var receiverScreenWidth: Int = 0
    private var receiverScreenHeight: Int = 0
    
    // 遥控端可视化区域信息
    private var visualizationView: View? = null
    private var visualizationWidth: Int = 0
    private var visualizationHeight: Int = 0
    
    // 精确的缩放比例
    private var preciseScaleX: Double = 1.0
    private var preciseScaleY: Double = 1.0
    
    // 初始化状态
    private var isInitialized: Boolean = false
    
    /**
     * 初始化坐标映射管理器
     * @param receiverWidth 接收端屏幕宽度
     * @param receiverHeight 接收端屏幕高度
     * @param visualizationView 遥控端可视化区域View
     */
    fun initialize(
        receiverWidth: Int,
        receiverHeight: Int,
        visualizationView: View
    ) {
        this.receiverScreenWidth = receiverWidth
        this.receiverScreenHeight = receiverHeight
        this.visualizationView = visualizationView

        AppLog.d("【坐标映射管理器】初始化:")
        AppLog.d("  接收端屏幕: ${receiverWidth}×${receiverHeight}")
        AppLog.d("  可视化View: ${visualizationView.javaClass.simpleName}")
        AppLog.d("  当前可视化区域尺寸: ${visualizationView.width}×${visualizationView.height}")

        // 立即计算精确的缩放比例（此时View应该已经布局完成）
        calculatePreciseScale()
    }
    
    /**
     * 计算精确的缩放比例
     * 基于实际的可视化区域尺寸
     */
    private fun calculatePreciseScale() {
        val view = visualizationView ?: run {
            AppLog.e("【坐标映射管理器】可视化View为null，无法计算缩放比例")
            return
        }

        visualizationWidth = view.width
        visualizationHeight = view.height

        AppLog.d("【坐标映射管理器】开始计算精确缩放比例:")
        AppLog.d("  可视化View类型: ${view.javaClass.simpleName}")
        AppLog.d("  可视化区域尺寸: ${visualizationWidth}×${visualizationHeight}")
        AppLog.d("  接收端屏幕尺寸: ${receiverScreenWidth}×${receiverScreenHeight}")

        if (visualizationWidth <= 0 || visualizationHeight <= 0) {
            AppLog.e("【坐标映射管理器】❌ 可视化区域尺寸无效: ${visualizationWidth}×${visualizationHeight}")
            AppLog.e("  这是导致坐标映射失败的根本原因！")
            AppLog.e("  View状态: isLaidOut=${view.isLaidOut}, visibility=${view.visibility}")
            AppLog.e("  View位置: left=${view.left}, top=${view.top}, right=${view.right}, bottom=${view.bottom}")
            return
        }

        if (receiverScreenWidth <= 0 || receiverScreenHeight <= 0) {
            AppLog.e("【坐标映射管理器】❌ 接收端屏幕尺寸无效: ${receiverScreenWidth}×${receiverScreenHeight}")
            return
        }

        // 计算X和Y方向的精确缩放比例
        preciseScaleX = visualizationWidth.toDouble() / receiverScreenWidth.toDouble()
        preciseScaleY = visualizationHeight.toDouble() / receiverScreenHeight.toDouble()

        isInitialized = true

        AppLog.d("【坐标映射管理器】✅ 精确缩放比例计算完成:")
        AppLog.d("  接收端屏幕: ${receiverScreenWidth}×${receiverScreenHeight}")
        AppLog.d("  可视化区域: ${visualizationWidth}×${visualizationHeight}")
        AppLog.d("  X方向缩放: ${"%.6f".format(preciseScaleX)}")
        AppLog.d("  Y方向缩放: ${"%.6f".format(preciseScaleY)}")
        AppLog.d("  🎯 支持独立的X/Y缩放，确保精确映射")

        // 验证缩放比例的合理性
        validateScaleRatio()
    }
    
    /**
     * 验证缩放比例的合理性
     */
    private fun validateScaleRatio() {
        val scaleRatio = preciseScaleX / preciseScaleY
        val tolerance = 0.05 // 5%的容差
        
        if (kotlin.math.abs(scaleRatio - 1.0) > tolerance) {
            AppLog.w("【坐标映射管理器】⚠️ X/Y缩放比例不一致:")
            AppLog.w("  X缩放: ${"%.6f".format(preciseScaleX)}")
            AppLog.w("  Y缩放: ${"%.6f".format(preciseScaleY)}")
            AppLog.w("  比例差异: ${"%.2f".format((scaleRatio - 1.0) * 100)}%")
            AppLog.w("  🎯 这可能导致窗口变形，建议检查可视化区域的宽高比")
        } else {
            AppLog.d("【坐标映射管理器】✅ X/Y缩放比例一致，映射精确")
        }
    }
    
    /**
     * 将遥控端坐标转换为接收端坐标
     * @param remoteX 遥控端X坐标
     * @param remoteY 遥控端Y坐标
     * @return 接收端坐标 Pair(receiverX, receiverY)
     */
    fun convertRemoteToReceiver(remoteX: Float, remoteY: Float): Pair<Float, Float> {
        if (!isInitialized) {
            AppLog.w("【坐标映射管理器】未初始化，使用原始坐标")
            return Pair(remoteX, remoteY)
        }
        
        // 使用精确的X/Y独立缩放
        val receiverX = (remoteX / preciseScaleX).toFloat()
        val receiverY = (remoteY / preciseScaleY).toFloat()
        
        AppLog.d("【坐标映射】遥控端 -> 接收端:")
        AppLog.d("  遥控端坐标: (${"%.1f".format(remoteX)}, ${"%.1f".format(remoteY)})")
        AppLog.d("  接收端坐标: (${"%.1f".format(receiverX)}, ${"%.1f".format(receiverY)})")
        AppLog.d("  X缩放: ${"%.6f".format(preciseScaleX)}, Y缩放: ${"%.6f".format(preciseScaleY)}")
        
        return Pair(receiverX, receiverY)
    }
    
    /**
     * 将接收端坐标转换为遥控端坐标
     * @param receiverX 接收端X坐标
     * @param receiverY 接收端Y坐标
     * @return 遥控端坐标 Pair(remoteX, remoteY)
     */
    fun convertReceiverToRemote(receiverX: Float, receiverY: Float): Pair<Float, Float> {
        if (!isInitialized) {
            AppLog.w("【坐标映射管理器】未初始化，使用原始坐标")
            return Pair(receiverX, receiverY)
        }
        
        // 使用精确的X/Y独立缩放
        val remoteX = (receiverX * preciseScaleX).toFloat()
        val remoteY = (receiverY * preciseScaleY).toFloat()
        
        AppLog.d("【坐标映射】接收端 -> 遥控端:")
        AppLog.d("  接收端坐标: (${"%.1f".format(receiverX)}, ${"%.1f".format(receiverY)})")
        AppLog.d("  遥控端坐标: (${"%.1f".format(remoteX)}, ${"%.1f".format(remoteY)})")
        
        return Pair(remoteX, remoteY)
    }
    
    /**
     * 验证边界映射的精确性
     * @param remoteBoundaryX 遥控端边界X坐标
     * @param remoteBoundaryY 遥控端边界Y坐标
     */
    fun validateBoundaryMapping(remoteBoundaryX: Float, remoteBoundaryY: Float) {
        if (!isInitialized) return

        val (receiverX, receiverY) = convertRemoteToReceiver(remoteBoundaryX, remoteBoundaryY)

        // 检查是否精确映射到接收端边界
        val xError = kotlin.math.abs(receiverX - receiverScreenWidth)
        val yError = kotlin.math.abs(receiverY - receiverScreenHeight)

        AppLog.d("【边界映射验证】:")
        AppLog.d("  遥控端边界: (${"%.1f".format(remoteBoundaryX)}, ${"%.1f".format(remoteBoundaryY)})")
        AppLog.d("  映射到接收端: (${"%.1f".format(receiverX)}, ${"%.1f".format(receiverY)})")
        AppLog.d("  接收端屏幕: (${receiverScreenWidth}, ${receiverScreenHeight})")
        AppLog.d("  X误差: ${"%.1f".format(xError)}px, Y误差: ${"%.1f".format(yError)}px")

        if (xError > 1.0f || yError > 1.0f) {
            AppLog.w("【边界映射验证】⚠️ 边界映射误差较大，可能导致窗口超出屏幕")
        } else {
            AppLog.d("【边界映射验证】✅ 边界映射精确")
        }
    }

    /**
     * 🎯 根本性诊断：测试坐标转换的往返精度
     * 用于诊断坐标映射的根本问题
     */
    fun diagnoseCoordinateMapping() {
        if (!isInitialized) {
            AppLog.w("【坐标诊断】坐标映射管理器未初始化")
            return
        }

        AppLog.d("【坐标诊断】开始坐标转换往返精度测试:")
        AppLog.d("  接收端屏幕: ${receiverScreenWidth}×${receiverScreenHeight}")
        AppLog.d("  可视化区域: ${visualizationWidth}×${visualizationHeight}")
        AppLog.d("  X缩放比例: ${"%.6f".format(preciseScaleX)}")
        AppLog.d("  Y缩放比例: ${"%.6f".format(preciseScaleY)}")

        // 测试关键边界点的往返转换
        val testPoints = listOf(
            Pair(0f, 0f) to "左上角",
            Pair(receiverScreenWidth.toFloat(), 0f) to "右上角",
            Pair(0f, receiverScreenHeight.toFloat()) to "左下角",
            Pair(receiverScreenWidth.toFloat(), receiverScreenHeight.toFloat()) to "右下角",
            Pair(receiverScreenWidth / 2f, receiverScreenHeight / 2f) to "中心点"
        )

        for ((point, name) in testPoints) {
            val (receiverX, receiverY) = point

            // 接收端 → 遥控端 → 接收端
            val (remoteX, remoteY) = convertReceiverToRemote(receiverX, receiverY)
            val (backReceiverX, backReceiverY) = convertRemoteToReceiver(remoteX, remoteY)

            val xError = kotlin.math.abs(backReceiverX - receiverX)
            val yError = kotlin.math.abs(backReceiverY - receiverY)

            AppLog.d("【坐标诊断】$name 往返测试:")
            AppLog.d("  原始接收端: (${"%.1f".format(receiverX)}, ${"%.1f".format(receiverY)})")
            AppLog.d("  转换到遥控端: (${"%.1f".format(remoteX)}, ${"%.1f".format(remoteY)})")
            AppLog.d("  转换回接收端: (${"%.1f".format(backReceiverX)}, ${"%.1f".format(backReceiverY)})")
            AppLog.d("  往返误差: X=${"%.3f".format(xError)}px, Y=${"%.3f".format(yError)}px")

            if (xError > 0.5f || yError > 0.5f) {
                AppLog.w("【坐标诊断】⚠️ $name 往返误差较大")
            }
        }

        // 测试可视化区域边界
        val visualBoundaryX = visualizationWidth.toFloat()
        val visualBoundaryY = visualizationHeight.toFloat()
        val (mappedReceiverX, mappedReceiverY) = convertRemoteToReceiver(visualBoundaryX, visualBoundaryY)

        AppLog.d("【坐标诊断】可视化边界映射:")
        AppLog.d("  可视化边界: (${"%.1f".format(visualBoundaryX)}, ${"%.1f".format(visualBoundaryY)})")
        AppLog.d("  映射到接收端: (${"%.1f".format(mappedReceiverX)}, ${"%.1f".format(mappedReceiverY)})")
        AppLog.d("  期望接收端边界: (${receiverScreenWidth}, ${receiverScreenHeight})")

        val boundaryXError = kotlin.math.abs(mappedReceiverX - receiverScreenWidth)
        val boundaryYError = kotlin.math.abs(mappedReceiverY - receiverScreenHeight)
        AppLog.d("  边界映射误差: X=${"%.1f".format(boundaryXError)}px, Y=${"%.1f".format(boundaryYError)}px")

        if (boundaryXError > 1.0f || boundaryYError > 1.0f) {
            AppLog.w("【坐标诊断】⚠️ 边界映射误差过大，这是导致窗口超出屏幕的根本原因！")
        } else {
            AppLog.d("【坐标诊断】✅ 边界映射精确")
        }
    }
    
    /**
     * 获取当前的缩放信息
     */
    fun getScaleInfo(): String {
        return if (isInitialized) {
            "X缩放: ${"%.6f".format(preciseScaleX)}, Y缩放: ${"%.6f".format(preciseScaleY)}"
        } else {
            "未初始化"
        }
    }
    
    /**
     * 检查是否已正确初始化
     */
    fun isReady(): Boolean = isInitialized
    
    /**
     * 获取兼容的统一缩放比例（用于向后兼容）
     */
    fun getCompatibleScale(): Double {
        return if (isInitialized) {
            kotlin.math.min(preciseScaleX, preciseScaleY)
        } else {
            1.0
        }
    }
}
