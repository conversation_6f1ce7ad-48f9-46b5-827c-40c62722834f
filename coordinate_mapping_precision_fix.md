# 坐标映射精度问题修复

## 问题描述
将遥控端远程接收端控制窗口上的文字窗口A的右边框移动到远程接收端控制窗口的最右侧与边框相切，而接收端相应的文字窗口A也会同步移动到屏幕的最右侧，但是没有与屏幕边框相切，接收端文字窗口A的右边框及部分文字窗口内容会超过屏幕最右侧。

## 问题根源分析

### 坐标映射精度问题
问题不是边界约束，而是**遥控端和接收端的相对尺寸映射不准确**：

1. **遥控端操作**：文字窗口右边框移动到遥控端控制窗口最右侧（相切）
2. **坐标转换**：遥控端坐标通过 `remoteControlScale` 转换为接收端坐标
3. **接收端结果**：文字窗口移动到屏幕最右侧，但**超出了屏幕边界**

### 缩放比例计算问题
**根本原因**：缩放比例计算使用的是**整个对话框尺寸**，但实际的**可视化区域比对话框小**：

```kotlin
// 问题代码：基于整个对话框尺寸计算
val (actualDialogWidth, actualDialogHeight) = calculateAdaptiveDialogSize()
val widthScale = actualDialogWidth.toDouble() / remoteWidth
val heightScale = actualDialogHeight.toDouble() / remoteHeight
remoteControlScale = minOf(widthScale, heightScale)
```

**问题分析：**
- **对话框尺寸**：包含标题栏、按钮等UI元素
- **可视化区域**：只包含 `windowVisualizationView` 的实际尺寸
- **结果**：缩放比例偏大，导致坐标映射不精确

### 坐标映射流程
1. **遥控端**：用户拖动到控制窗口边界 (x = visualizationWidth)
2. **坐标转换**：`actualX = x / remoteControlScale`
3. **接收端**：应用坐标 `actualX`
4. **问题**：如果 `remoteControlScale` 偏大，`actualX` 会偏小，导致窗口超出边界

## 修复方案

### 核心修复思路
**基于实际可视化区域尺寸计算缩放比例**，而不是整个对话框尺寸：

1. **延迟计算**：等待可视化区域布局完成后重新计算
2. **精确尺寸**：使用 `windowVisualizationView` 的实际 `width` 和 `height`
3. **精度检查**：只有差异超过0.1%时才更新缩放比例

### 已实施的修复

#### 1. 延迟重新计算缩放比例
```kotlin
// 在 calculateRemoteControlScale() 中添加
windowVisualizationView?.post {
    recalculateScaleBasedOnVisualizationArea()
}
```

#### 2. 基于实际可视化区域的精确计算
```kotlin
private fun recalculateScaleBasedOnVisualizationArea() {
    val actualVisualizationWidth = windowVisualizationView?.width ?: 0
    val actualVisualizationHeight = windowVisualizationView?.height ?: 0

    if (actualVisualizationWidth > 0 && actualVisualizationHeight > 0) {
        val remoteWidth = remoteReceiverConnection.screenWidth
        val remoteHeight = remoteReceiverConnection.screenHeight

        // 基于实际可视化区域计算精确的缩放比例
        val widthScale = actualVisualizationWidth.toDouble() / remoteWidth
        val heightScale = actualVisualizationHeight.toDouble() / remoteHeight
        val newRemoteControlScale = minOf(widthScale, heightScale)

        // 检查是否需要更新缩放比例
        val scaleDifference = kotlin.math.abs(newRemoteControlScale - remoteControlScale)
        if (scaleDifference > 0.001) { // 只有差异超过0.1%时才更新
            remoteControlScale = newRemoteControlScale
        }
    }
}
```

## 修复后的坐标映射流程

### 精确的缩放比例计算
1. **初始计算**：基于对话框尺寸进行初步计算
2. **延迟重新计算**：等待可视化区域布局完成
3. **精确计算**：基于实际可视化区域尺寸重新计算
4. **精度检查**：只有显著差异时才更新

### 精确的坐标映射
1. **遥控端**：用户拖动到可视化区域边界 (x = actualVisualizationWidth)
2. **坐标转换**：`actualX = x / preciseRemoteControlScale`
3. **接收端**：应用精确坐标 `actualX`
4. **结果**：窗口精确对齐到屏幕边界，不会超出

## 测试验证

### 测试步骤
1. **创建文字窗口**：在接收端创建文字窗口
2. **建立远程连接**：遥控端连接到接收端
3. **测试边界对齐**：
   - 将文字窗口拖动到遥控端控制窗口的最右侧
   - 确认遥控端窗口与控制窗口边界相切
   - 检查接收端窗口是否与屏幕边界相切
4. **测试其他边界**：测试左边界、上边界、下边界的对齐精度
5. **测试不同分辨率**：在不同分辨率的设备上测试

### 预期结果
- ✅ **精确边界对齐**：遥控端边界对齐时，接收端也精确对齐
- ✅ **无超出边界**：接收端窗口不会超出屏幕边界
- ✅ **坐标映射精确**：遥控端和接收端的相对位置完全一致
- ✅ **不同分辨率兼容**：在各种分辨率下都保持精确映射

## 技术细节

### 缩放比例差异分析
```kotlin
AppLog.d("【窗口可视化】🎯 基于实际可视化区域重新计算缩放比例:")
AppLog.d("  接收端屏幕: ${remoteWidth}×${remoteHeight}")
AppLog.d("  实际可视化区域: ${actualVisualizationWidth}×${actualVisualizationHeight}")
AppLog.d("  旧缩放比例: ${"%.3f".format(oldScale)}")
AppLog.d("  新缩放比例: ${"%.3f".format(newRemoteControlScale)}")
AppLog.d("  差异: ${"%.3f".format(scaleDifference)} (${(scaleDifference/oldScale*100).toInt()}%)")
```

### 精度阈值
- **更新阈值**：0.001 (0.1%)
- **原因**：避免微小的布局差异导致频繁更新
- **效果**：只有显著的尺寸差异才会触发重新计算

### 布局时机
- **初始计算**：在 `calculateRemoteControlScale()` 中进行
- **延迟重新计算**：使用 `post {}` 确保布局完成后执行
- **时机保证**：确保可视化区域的实际尺寸已确定

## 关键修改文件
1. `app/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.kt`
   - 修改 `calculateRemoteControlScale()` 方法
   - 新增 `recalculateScaleBasedOnVisualizationArea()` 方法

## 修复效果
这个修复确保了遥控端和接收端之间的坐标映射精度，解决了边界对齐不准确的问题。现在当用户在遥控端将窗口拖动到边界时，接收端的窗口也会精确地对齐到相应的边界，不会出现超出屏幕的情况。
